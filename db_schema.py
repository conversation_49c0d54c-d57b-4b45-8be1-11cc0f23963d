from pymongo import MongoClient
from datetime import datetime

"""
MongoDB Schema for Egg Price Agent

This module defines the MongoDB schema and connection utilities for storing
user queries and agent responses from the Egg Price Agent.

Collection: user_queries
Schema:
- query_id: ObjectId (automatically generated by MongoDB)
- query_text: String (the user's original question)
- response_text: String (the agent's response)
- query_timestamp: DateTime (when the query was made)
- city: String (optional - the city mentioned in the query, if any)
- price_data: Object (optional - structured price data returned)
"""


class EggPriceDatabase:
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="egg_price_agent"):
        """
        Initialize the database connection
        
        Args:
            connection_string (str): MongoDB connection string (default is MongoDB Atlas format)
            db_name (str): Name of the database
        """
        try:
            self.client = MongoClient(connection_string)
            self.db = self.client[db_name]
            self.user_queries = self.db.user_queries
            # Test connection
            self.client.admin.command('ping')
            print("Connected successfully to MongoDB")
        except Exception as e:
            print(f"MongoDB connection error: {e}")
            raise
        
    def store_query(self, query_text, response_text, city=None, price_data=None):
        """
        Store a user query and the agent's response
        
        Args:
            query_text (str): The user's original question
            response_text (str): The agent's response
            city (str, optional): City mentioned in the query
            price_data (dict, optional): Structured price data returned
            
        Returns:
            str: ID of the inserted document
        """
        document = {
            "query_text": query_text,
            "response_text": response_text,
            "query_timestamp": datetime.now(),
        }
        
        # Add optional fields if provided
        if city:
            document["city"] = city
        
        if price_data:
            document["price_data"] = price_data
            
        result = self.user_queries.insert_one(document)
        return str(result.inserted_id)
    
    def get_recent_queries(self, limit=10):
        """
        Get the most recent user queries
        
        Args:
            limit (int): Maximum number of queries to return
            
        Returns:
            list: List of recent query documents
        """
        return list(self.user_queries.find().sort("query_timestamp", -1).limit(limit))
    
    def get_queries_by_city(self, city, limit=10):
        """
        Get queries for a specific city
        
        Args:
            city (str): City to filter by
            limit (int): Maximum number of queries to return
            
        Returns:
            list: List of query documents for the specified city
        """
        return list(self.user_queries.find({"city": city}).sort("query_timestamp", -1).limit(limit))
    
    def close_connection(self):
        """
        Close the MongoDB connection
        """
        self.client.close()