# Commodity Price Analytics Dashboard

This dashboard provides comprehensive visualizations and analytics for commodity prices (Eggs, Copra, and Chicken) across different cities in India. It features interactive charts, price comparisons, anomaly detection, and scraper performance monitoring.

## Features

1. **Time-Series Price Graph**
   - Interactive line charts showing price trends over time
   - Separate tabs for Eggs, Copra, and Chicken
   - Multiple lines for different cities

2. **City-wise Price Comparison**
   - Bar chart comparing average prices across cities
   - Side-by-side comparison of all commodities
   - Interactive legends and tooltips

3. **Scraper Performance Analysis**
   - Pie chart showing success vs failure rates
   - Color-coded performance metrics
   - Weekly/monthly statistics

4. **Price Anomaly Detection**
   - Scatter plots highlighting unusual price movements
   - Statistical anomaly detection using z-score method
   - Visual indicators for price spikes and drops

5. **Slack Notification Flow**
   - Sankey diagram showing notification workflow
   - Color-coded success/error paths
   - Real-time status updates

## Setup Instructions

1. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

2. Ensure the SQLite database (commodity_prices.db) is present in the root directory

3. Run the dashboard:
   ```bash
   streamlit run dashboard.py
   ```

4. The dashboard will open automatically in your default web browser

## Data Sources

The dashboard pulls data from the following sources:
- Egg prices database table
- Copra prices database table
- Chicken prices database table
- Scraper performance logs

## Customization

You can customize the dashboard by:
- Adjusting the anomaly detection threshold in the code
- Modifying the color schemes of the charts
- Adding new visualization components
- Changing the time range for analysis

## Troubleshooting

If you encounter any issues:
1. Verify that all required packages are installed correctly
2. Check the database connection and table structure
3. Ensure you have sufficient data in the database
4. Check the Streamlit documentation for specific errors