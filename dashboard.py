import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# Create graphs directory if it doesn't exist
os.makedirs('graphs', exist_ok=True)

# Create sample data
def create_sample_data():
    # Generate dates for the last 30 days
    dates = [(datetime.now() - timedelta(days=x)).strftime('%Y-%m-%d') for x in range(30)]
    cities = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Kolkata']
    
    # Create sample dataframes
    egg_data = []
    copra_data = []
    chicken_data = []
    
    for city in cities:
        for date in dates:
            # Egg prices (₹5-8 per egg)
            egg_data.append({
                'date': date,
                'city': city,
                'price': round(5 + 3 * np.random.random(), 2)
            })
            
            # Copra prices (₹80-120 per kg)
            copra_data.append({
                'date': date,
                'city': city,
                'price': round(80 + 40 * np.random.random(), 2)
            })
            
            # Chicken prices (₹150-200 per kg)
            chicken_data.append({
                'date': date,
                'city': city,
                'price': round(150 + 50 * np.random.random(), 2)
            })
    
    return pd.DataFrame(egg_data), pd.DataFrame(copra_data), pd.DataFrame(chicken_data)

# Load sample data
egg_df, copra_df, chicken_df = create_sample_data()

# 1. Time-Series Price Graph
fig_egg = px.line(egg_df, x='date', y='price', color='city',
                  title='Egg Prices Over Time')
fig_egg.write_html('graphs/egg_prices_time_series.html')

fig_copra = px.line(copra_df, x='date', y='price', color='city',
                    title='Copra Prices Over Time')
fig_copra.write_html('graphs/copra_prices_time_series.html')

fig_chicken = px.line(chicken_df, x='date', y='price', color='city',
                      title='Chicken Prices Over Time')
fig_chicken.write_html('graphs/chicken_prices_time_series.html')

# 2. City-wise Price Comparison
# Calculate average prices per city for each commodity
egg_avg = egg_df.groupby('city')['price'].mean().reset_index()
copra_avg = copra_df.groupby('city')['price'].mean().reset_index()
chicken_avg = chicken_df.groupby('city')['price'].mean().reset_index()

# Create grouped bar chart
fig_comparison = go.Figure()

fig_comparison.add_trace(go.Bar(
    x=egg_avg['city'],
    y=egg_avg['price'],
    name='Egg'
))

fig_comparison.add_trace(go.Bar(
    x=copra_avg['city'],
    y=copra_avg['price'],
    name='Copra'
))

fig_comparison.add_trace(go.Bar(
    x=chicken_avg['city'],
    y=chicken_avg['price'],
    name='Chicken'
))

fig_comparison.update_layout(
    title='Average Price Comparison of Commodities Across Cities',
    xaxis_title='Cities',
    yaxis_title='Price (INR)'
)

fig_comparison.write_html('graphs/price_comparison.html')

# 3. Scraper Performance Graph
# Create pie chart for scraper performance
stats = {
    'success': 85,
    'failure': 10,
    'retry': 5
}

fig_scraper = px.pie(
    values=list(stats.values()),
    names=list(stats.keys()),
    title='Scraper Success vs Failure Count',
    color_discrete_sequence=['#00FF00', '#FF0000', '#FFA500']
)

fig_scraper.write_html('graphs/scraper_performance.html')

# 4. Price Anomaly Detection
# Function to detect anomalies (simple z-score method)
def detect_anomalies(df, threshold=2):
    mean = df['price'].mean()
    std = df['price'].std()
    df['is_anomaly'] = abs(df['price'] - mean) > (threshold * std)
    return df

# Create anomaly detection plots
egg_anomalies = detect_anomalies(egg_df.copy())
fig_egg_anomaly = px.scatter(egg_anomalies, x='date', y='price',
                            color='is_anomaly',
                            title='Egg Price Anomalies')
fig_egg_anomaly.write_html('graphs/egg_anomalies.html')

copra_anomalies = detect_anomalies(copra_df.copy())
fig_copra_anomaly = px.scatter(copra_anomalies, x='date', y='price',
                              color='is_anomaly',
                              title='Copra Price Anomalies')
fig_copra_anomaly.write_html('graphs/copra_anomalies.html')

chicken_anomalies = detect_anomalies(chicken_df.copy())
fig_chicken_anomaly = px.scatter(chicken_anomalies, x='date', y='price',
                                color='is_anomaly',
                                title='Chicken Price Anomalies')
fig_chicken_anomaly.write_html('graphs/chicken_anomalies.html')

# 5. Slack Notification Flow
# Create a Sankey diagram for notification flow
node_labels = ['Scraper Task', 'Validation', 'Success', 'Error']
node_colors = ['#808080', '#808080', '#00FF00', '#FF0000']

fig_flow = go.Figure(data=[go.Sankey(
    node=dict(
        pad=15,
        thickness=20,
        line=dict(color="black", width=0.5),
        label=node_labels,
        color=node_colors
    ),
    link=dict(
        source=[0, 0, 1, 1],
        target=[1, 1, 2, 3],
        value=[80, 20, 80, 20]
    )
)])

fig_flow.update_layout(title_text="Slack Notification Flow", font_size=10)
fig_flow.write_html('graphs/slack_notification_flow.html')

print("All graphs have been generated and saved in the 'graphs' directory.")