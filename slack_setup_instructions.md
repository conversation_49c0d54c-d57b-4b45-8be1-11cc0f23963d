# Slack Integration Setup Instructions

## Quick Setup (5 minutes)

### Step 1: Create Slack Webhook
1. Go to your Slack workspace
2. Click on your workspace name → Settings & administration → Manage apps
3. Search for "Incoming Webhooks" and add it to your workspace
4. Click "Add to Slack"
5. Choose the channel where you want notifications (e.g., `#commodity-alerts`)
6. Click "Add Incoming WebHooks integration"
7. Copy the Webhook URL (looks like: `*****************************************************************************`)

### Step 2: Configure the Webhook URL
Open `slack_notifier.py` and replace this line:
```python
"INSERT_YOUR_WEBHOOK_URL_HERE"
```

With your actual webhook URL:
```python
"https://hooks.slack.com/services/YOUR/WEBHOOK/URL"
```

### Step 3: Test the Integration
Run the test:
```bash
python slack_notifier.py
```

You should see test messages in your Slack channel!

## Usage

### Run Individual Scrapers
```bash
# Egg scraper with Slack notifications
python egg_scraper_with_slack.py

# Copra scraper with Slack notifications  
python copra_scraper_with_slack.py

# Chicken scraper with Slack notifications
python chicken_scraper_with_slack.py
```

### Run All Scrapers Together
```bash
python run_all_scrapers_with_slack.py
```

## Expected Slack Messages

You'll receive these types of messages:

✅ **Success Messages:**
- `✅ SUCCESSFULLY SCRAPED PRICES FOR EGG SCRAPER`
- `✅ SUCCESSFULLY SCRAPED PRICES FOR COPRA SCRAPER`
- `✅ SUCCESSFULLY SCRAPED PRICES FOR CHICKEN SCRAPER`

🚨 **Error Messages:**
- `🚨 ERROR SCRAPING PRICES FOR EGG SCRAPER`
- `🚨 ERROR SCRAPING PRICES FOR COPRA SCRAPER`
- `🚨 ERROR SCRAPING PRICES FOR CHICKEN SCRAPER`

## Alternative Setup (Environment Variable)

Instead of editing the file, you can set an environment variable:

### Windows:
```cmd
set SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

### Linux/Mac:
```bash
export SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

### Using .env file:
Create a `.env` file in your project directory:
```
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

## Troubleshooting

### No messages in Slack?
1. Check if the webhook URL is correct
2. Verify the channel exists and the webhook has permission
3. Check console output for error messages

### Messages appear in console but not Slack?
- The webhook URL is not configured properly
- Check your internet connection
- Verify Slack workspace permissions

### Want to disable Slack temporarily?
Set the webhook URL to `"INSERT_YOUR_WEBHOOK_URL_HERE"` and messages will print to console only.

## File Structure

```
your_project/
├── slack_notifier.py              # Core Slack notification system
├── egg_scraper_with_slack.py      # Enhanced egg scraper
├── copra_scraper_with_slack.py    # Enhanced copra scraper  
├── chicken_scraper_with_slack.py  # Enhanced chicken scraper
├── run_all_scrapers_with_slack.py # Master script to run all
└── slack_setup_instructions.md    # This file
```

## Dependencies

Make sure you have these installed:
```bash
pip install requests pymongo fastapi uvicorn pydantic python-dotenv beautifulsoup4 playwright
playwright install chromium
```

## Support

If you encounter any issues:
1. Check the console output for detailed error messages
2. Verify MongoDB is running
3. Ensure all dependencies are installed
4. Test the Slack webhook URL manually

That's it! Your scrapers will now send notifications to Slack automatically. 🚀
